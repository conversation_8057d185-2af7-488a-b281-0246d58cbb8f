import {
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { Maximize2, Minimize2 } from '@bookln/icon-lucide';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { Slider } from 'react-native-awesome-slider';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  CUSTOM_SLIDER_THEME,
  VIDEO_EVENT_UPDATE_TIME,
  VIDEO_PLAYBACK_RATE_OPTIONS,
} from '../../constants/video';
import { formatVideoTime } from '../../helper/videoHelper';
import { useBookVideoTabContext } from '../contexts';

export type VideoControlsProps = {
  currentTime: number;
  duration: number;
  isFullscreen: boolean;
  isVideoFinished?: boolean;
  playbackRate: number;
  isVisible?: boolean;
  onFullscreenToggle: () => void;
  onSeek?: (value: number) => void;
  handleChangePlaybackRate: (rate: number) => void;
};

export const VideoControls = memo<VideoControlsProps>((props) => {
  const {
    currentTime,
    duration,
    isFullscreen,
    isVideoFinished,
    playbackRate,
    isVisible = true,
    onFullscreenToggle,
    onSeek,
    handleChangePlaybackRate,
  } = props;

  const { handleUserInteraction, setIsSliderDragging, isSliderDragging } =
    useBookVideoTabContext();

  const progress = useSharedValue(0);
  const min = useSharedValue(0);
  const max = useSharedValue(100);

  // 控制栏透明度动画
  const opacity = useSharedValue(isVisible ? 1 : 0);

  // 动画样式
  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(opacity.value, { duration: 300 }),
      transform: [
        {
          translateY: withTiming(opacity.value === 0 ? 20 : 0, {
            duration: 300,
          }),
        },
      ],
    };
  });

  // 监听可见性变化，更新动画
  useEffect(() => {
    opacity.value = isVisible ? 1 : 0;
  }, [isVisible, opacity]);

  const timeText = useMemo(() => {
    return `${formatVideoTime(currentTime)} / ${formatVideoTime(duration)}`;
  }, [currentTime, duration]);

  const calculateProgress = useCallback(
    (current: number, total: number): number => {
      // 处理 NaN 和无效值的情况
      if (
        Number.isNaN(current) ||
        Number.isNaN(total) ||
        total <= 0 ||
        current < 0
      ) {
        return 0;
      }
      return Math.round(Math.min((current / total) * 100, 100));
    },
    [],
  );

  const renderThumb = useCallback(() => {
    return (
      <YTView
        backgroundColor={'white'}
        width={20}
        height={20}
        borderRadius={10}
      />
    );
  }, []);

  // 获取下一个播放速率
  const getNextPlaybackRate = useCallback((currentRate: number): number => {
    const currentIndex = VIDEO_PLAYBACK_RATE_OPTIONS.indexOf(currentRate);
    // 如果当前速率不在选项中，返回第一个选项
    if (currentIndex === -1) {
      return VIDEO_PLAYBACK_RATE_OPTIONS[0] ?? 1;
    }
    // 循环到下一个选项，如果是最后一个则回到第一个
    const nextIndex = (currentIndex + 1) % VIDEO_PLAYBACK_RATE_OPTIONS.length;

    return VIDEO_PLAYBACK_RATE_OPTIONS[nextIndex] ?? 1;
  }, []);

  const getBubbleText = useCallback(
    (current: number) => {
      const time = Math.max(duration * (current / 100), 0);
      return formatVideoTime(time);
    },
    [duration],
  );

  // 监听 currentTime 和 duration 变化，更新 progress
  useEffect(() => {
    // 当用户正在拖动进度条时，不要更新 progress，避免竞态问题
    if (isSliderDragging) {
      return;
    }

    const newValue = calculateProgress(currentTime, duration);

    // 如果视频播放完毕，直接设置为100%
    if (isVideoFinished) {
      progress.value = 100;
      return;
    }

    // 只有当新值与当前值有差异时才更新，或者是初始化时
    if (
      Math.abs(newValue - progress.value) >= VIDEO_EVENT_UPDATE_TIME ||
      (progress.value === 0 && newValue > 0)
    ) {
      progress.value = newValue;
    }
  }, [
    calculateProgress,
    currentTime,
    duration,
    progress,
    isVideoFinished,
    isSliderDragging,
  ]);

  // 如果不可见且动画完成，不渲染组件
  if (!isVisible && opacity.value === 0) {
    return null;
  }

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <YTYStack bg='rgba(0,0,0,0.45)' ai='center' width='100%'>
        <YTXStack
          alignItems='center'
          justifyContent='space-between'
          width='100%'
          bg='transparent'
          p={8}
          borderBottomWidth={1}
          borderBottomColor='#FFFFFF87'
          borderStyle='solid'
        >
          <YTXStack alignItems='center' bg='transparent'>
            <YTText fontSize={12} color='#FCFCFC'>
              {timeText}
            </YTText>
          </YTXStack>

          <YTXStack alignItems='center' gap={12} bg='transparent'>
            <YTTouchable
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={() => {
                handleUserInteraction?.();
                const nextRate = getNextPlaybackRate(playbackRate);
                handleChangePlaybackRate(nextRate);
              }}
            >
              <YTText fontSize={12} color='#FCFCFC'>
                {playbackRate}x
              </YTText>
            </YTTouchable>

            <YTTouchable
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={() => {
                handleUserInteraction?.();
                onFullscreenToggle();
              }}
            >
              {isFullscreen ? (
                <Minimize2 color='#FCFCFC' size={16} />
              ) : (
                <Maximize2 color='#FCFCFC' size={16} />
              )}
            </YTTouchable>
          </YTXStack>
        </YTXStack>

        <YTView width={'$full'} backgroundColor={'transparent'} height={20}>
          <Slider
            progress={progress}
            minimumValue={min}
            maximumValue={max}
            sliderHeight={20}
            disableTapEvent={false}
            onSlidingStart={() => {
              handleUserInteraction?.();
              setIsSliderDragging?.(true);
            }}
            onValueChange={(value) => {
              progress.value = Math.round(value);
            }}
            onSlidingComplete={(value) => {
              const roundedValue = Math.round(value);
              onSeek?.(roundedValue);
              progress.value = roundedValue;

              // 延迟重置拖动状态，确保 seek 操作完成
              setTimeout(() => {
                setIsSliderDragging?.(false);
              }, 100);
            }}
            theme={CUSTOM_SLIDER_THEME}
            renderMark={({ index }) => {
              if (index === 0) {
                return null;
              }
              return <YTView width={2} height={20} backgroundColor={'black'} />;
            }}
            renderThumb={renderThumb}
            bubble={getBubbleText}
          />
        </YTView>
      </YTYStack>
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
    zIndex: 10,
  },
});
